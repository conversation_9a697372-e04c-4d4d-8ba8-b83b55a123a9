import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { createrequestservice } from '../../services/createrequest.service';
import { PropertyService } from '../../services/property.service';
import { Router } from '@angular/router';
import { BehaviorSubject, forkJoin, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import Swal from 'sweetalert2';
import {
  STEPPER_CONFIG,
  SPECIALIZATION_SCOPE_OPTIONS,
  TYPE_OPTIONS,
  FILE_VALIDATION_CONFIG,
  OptionItem,
  TranslatableOptionItem
} from './stepper-modal.constants';
import { StepperInputConfigService } from './stepper-input-config.service';
import { TranslationService } from 'src/app/modules/i18n';
import { TranslateService } from '@ngx-translate/core';
import { PROPERTY_TYPES } from 'src/app/shared/interfaces/property-types.interface';

@Component({
  selector: 'app-stepper-modal',
  templateUrl: './stepper-modal.component.html',
  styleUrls: ['./stepper-modal.component.scss'],
})
export class StepperModalComponent implements OnInit {
  totalSteps = STEPPER_CONFIG.TOTAL_STEPS;
  currentStep = STEPPER_CONFIG.INITIAL_STEP;
  userId: number;
  userRole: string;
  stepForms: FormGroup[] = [];
  currentInputs: any[] = [];
  validationErrors: any[] = [];
  showErrorList = false;

  private citiesSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);
  private areasSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);
  private subAreasSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);

  cities$ = this.citiesSubject.asObservable();
  areas$ = this.areasSubject.asObservable();
  subAreas$ = this.subAreasSubject.asObservable();

  selectedCityId: number | null = null;
  selectedCityName: string = '';
  selectedAreaId: number | null = null;
  selectedAreaName: string = '';
  selectedSubAreaName: string = '';
  isLoadingCities = false;
  isSubmitting = false;

  specializationScopeOptions = SPECIALIZATION_SCOPE_OPTIONS;
  typeOptions = TYPE_OPTIONS;
  unitTypeOptions: OptionItem[] = [];
  fieldToStepMap: { [key: string]: number } = {};
  stepNames = STEPPER_CONFIG.STEP_NAMES;

  // Get adjusted step names (accounting for skipped step 4)
  getAdjustedStepNames(): { [key: number]: string } {
    if (this.shouldSkipStep4()) {
      const adjustedNames: { [key: number]: string } = {};
      Object.keys(this.stepNames).forEach(key => {
        const stepNum = parseInt(key);
        if (stepNum < 4) {
          adjustedNames[stepNum] = this.stepNames[stepNum];
        } else if (stepNum > 4) {
          adjustedNames[stepNum - 1] = this.stepNames[stepNum];
        }
        // Skip step 4
      });
      return adjustedNames;
    }
    return this.stepNames;
  }

  // Get adjusted total steps
  getAdjustedTotalSteps(): number {
    return this.shouldSkipStep4() ? this.totalSteps - 1 : this.totalSteps;
  }

  // Get adjusted current step for display
  getAdjustedCurrentStep(): number {
    if (this.shouldSkipStep4() && this.currentStep > 4) {
      return this.currentStep - 1;
    }
    return this.currentStep;
  }

  // Get current step name safely
  getCurrentStepName(): string {
    const adjustedNames = this.getAdjustedStepNames();
    const adjustedStep = this.getAdjustedCurrentStep();
    return adjustedNames[adjustedStep] || `Step ${adjustedStep}`;
  }

  // Enhanced multiselect properties
  private searchQueries: { [fieldName: string]: string } = {};

  constructor(
    private fb: FormBuilder,
    private createRequestService: createrequestservice,
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private stepperInputConfigService: StepperInputConfigService,
    public translationService: TranslationService,
    public translate: TranslateService
  ) {}

  ngOnInit(): void {
    const userJson = localStorage.getItem('currentUser');
    const user = userJson ? JSON.parse(userJson) : null;
    this.userId = user?.id;
    this.userRole = user?.role;

    this.initForms();
    this.loadInitialData();
    this.loadStepInputs();
  }

  initForms(): void {
       // Only initialize forms if they don't exist or are empty
    if (!this.stepForms || this.stepForms.length === 0) {
      console.log('Initializing forms for the first time');
      this.stepForms = Array(this.totalSteps)
        .fill(null)
        .map(() => this.fb.group({}));
      this.stepForms[0] = this.fb.group({
        specializationScope: ['', Validators.required],
        type: ['', Validators.required],
        unitType: ['', Validators.required],
      });
    } else {
      console.log('Forms already exist, preserving data');
    }
  }

  loadInitialData(): void {
    this.isLoadingCities = true;
    forkJoin({
      cities: this.propertyService.getCities().pipe(
        map((response: any) => {
          const cities = response.data || response;
          return cities.map((city: any) => ({
            key: city.name_en,
            value: city.id,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching cities:', error);
          Swal.fire('Error', 'Failed to fetch cities.', 'error');
          return of([]);
        })
      ),
      unitTypes: this.propertyService.getUnitTypes().pipe(
        map((response: any) => {
          const unitTypes = response.data || response;
          return Object.entries(unitTypes).map(([key, value]) => ({
            key,
            value: value as string,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching unit types:', error);
          Swal.fire('Error', 'Failed to fetch unit types.', 'error');
          return of([]);
        })
      ),
    }).subscribe({
      next: ({ cities, unitTypes }) => {
        this.citiesSubject.next(cities);
        // this.unitTypeOptions = unitTypes;
        this.unitTypeOptions = [
        ...unitTypes,
      { key: 'factory lands or factory', value: 'factory_lands' },
      { key: 'warehouses lands or warehouses', value: 'warehouses' }
      ];
        this.isLoadingCities = false;
        this.cdr.markForCheck();
      },
      error: (err) => {
        this.isLoadingCities = false;
        console.error('Error loading initial data:', err);
        Swal.fire('Error', 'Failed to load initial data.', 'error');
        this.cdr.markForCheck();
      },
    });
  }

  loadStepInputs(): void {
    const type = this.stepForms[0].get('type')?.value;
    const unitType = this.stepForms[0].get('unitType')?.value;

    if (this.currentStep > 1) {
      this.currentInputs = this.stepperInputConfigService.getInputsForKey(this.getConfigKey(), this.currentStep, this);

      // Preserve existing form data
      const currentForm = this.stepForms[this.currentStep - 1];
      const existingValues = currentForm ? currentForm.value : {};

      console.log(`Loading step ${this.currentStep}, existing values:`, existingValues);

      const formControls = this.currentInputs.reduce((acc: any, input: any) => {
         // Use existing value if available, otherwise use default
        const existingValue = existingValues[input.name];
        const defaultValue = input.type === 'multiSelect' ? [] : '';
        const valueToUse = existingValue !== undefined ? existingValue : defaultValue;

        acc[input.name] = [valueToUse, input.validators || []];
        return acc;
      }, {});

      // Only create new form if it doesn't exist, otherwise update existing form
      if (!this.stepForms[this.currentStep - 1]) {
        this.stepForms[this.currentStep - 1] = this.fb.group(formControls);
      } else {
        // Update existing form with new controls while preserving values
        const form = this.stepForms[this.currentStep - 1];

        // Add new controls that don't exist
        Object.keys(formControls).forEach(key => {
          if (!form.contains(key)) {
            form.addControl(key, this.fb.control(formControls[key][0], formControls[key][1]));
            console.log(`Added control: ${key} with value:`, formControls[key][0]);
          } else {
            // Update existing control value if it's empty but we have a saved value
            const control = form.get(key);
            if (control && !control.value && formControls[key][0]) {
              control.setValue(formControls[key][0]);
              console.log(`Updated control: ${key} with value:`, formControls[key][0]);
            }
          }
        });
      }
      if (this.currentStep === 2) {
        const form = this.stepForms[1];
        const existingValues = form.value;

        console.log('Step 2 existing values:', existingValues);

        // Add controls only if they don't exist, preserving existing values
        if (!form.contains('cityId')) {
          form.addControl('cityId', this.fb.control(existingValues.cityId || '', Validators.required));
        }
        if (!form.contains('areaId')) {
          form.addControl('areaId', this.fb.control(existingValues.areaId || '', Validators.required));
        }
        if (!form.contains('subAreaId')) {
          form.addControl('subAreaId', this.fb.control(existingValues.subAreaId || ''));
        }
        if (!form.contains('compoundName')) {
          form.addControl(
            'compoundName',
            this.fb.control(existingValues.compoundName || '', (this.getInsideCompoundPrivilege() && type == 'sell') ? Validators.required : null)
          );
        }

        console.log((this.getInsideCompoundPrivilege() && type == 'sell') ? Validators.required : null);

        // Add additional fields for outside compound scenarios
        if (!this.getInsideCompoundPrivilege()) {
          // Add detailedAddress and addressLink for rent-out and sell outside compound scenarios
          if ((this.getRentOutsideCompoundInputs() || this.getSellOutsideCompoundInputs())) {
             if (!form.contains('detailedAddress')) {
              form.addControl('detailedAddress', this.fb.control(existingValues.detailedAddress || '', Validators.required));
            }
            if (!form.contains('addressLink')) {
              form.addControl('addressLink', this.fb.control(existingValues.addressLink || ''));
            }
          }

          // Add locationSuggestions for all outside compound scenarios
          if (!form.contains('locationSuggestions')) {
            form.addControl('locationSuggestions', this.fb.control(existingValues.locationSuggestions || false));
          }
        }

        // Add Project Management and Project Constructor for sell inside compound scenarios
        if (this.getSellInsideCompoundInputs()) {
          if (!form.contains('projectManagement')) {
            form.addControl('projectManagement', this.fb.control(existingValues.projectManagement || '', Validators.required));
          }
          if (!form.contains('projectConstructor')) {
            form.addControl('projectConstructor', this.fb.control(existingValues.projectConstructor || '', Validators.required));
          }
        }

        console.log('Step 2 form after setup:', form.value);
        console.log('Step 2 form controls:', Object.keys(form.controls));
      }
    } else {
      this.currentInputs = [
        {
          name: 'specializationScope',
          type: 'select',
          label: 'Specialization Scope',
          options: this.specializationScopeOptions,
          validators: [Validators.required],
        },
        {
          name: 'type',
          type: 'select',
          label: 'Type',
          options: this.getFilteredTypeOptions(),
          validators: [Validators.required],
        },
        {
          name: 'unitType',
          type: 'select',
          label: 'Unit Type',
          options: this.getFilteredUnitTypeOptions(),
          validators: [Validators.required],
        },
      ];
    }
    this.cdr.markForCheck();
  }

  getSellInsideCompoundInputs(): boolean {

    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'resale_inside_compound',
      'primary_inside_compound',
    ];
    const typeScopes = [
      'sell'
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getRentOutInsideCompoundInputs(): boolean {

    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'rentals_inside_compound',
    ];
    const typeScopes = [
      'rent_out',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getRentOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'rentals_outside_compound',
    ];
    const typeScopes = [
      'rent_out',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getRentInOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'rentals_outside_compound',
    ];
    const typeScopes = [
      'rent_in',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getSellOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'purchase_sell_outside_compound',
    ];
    const typeScopes = [
      'sell',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getPurchaseOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'purchase_sell_outside_compound',
    ];
    const typeScopes = [
      'purchasing',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getMallPrivilege(): boolean {
    const type = this.stepForms[0].get('type')?.value;
    const unitType = this.stepForms[0].get('unitType')?.value;
    const mallUnitTypes = ['administrative_units', 'medical_clinics', 'pharmacies', 'commercial_stores'];
    const hasTargetType = ['sell', 'rent_out'].includes(type);
    return mallUnitTypes.includes(unitType) && hasTargetType && !this.getInsideCompoundPrivilege();
  }

  getUnitAndBuildingNumber(): boolean {
    const type = this.stepForms[0].get('type')?.value;
    return ['sell', 'rent_out'].includes(type);
  }

  getVillageName(): boolean {
    const unitType = this.stepForms[0].get('unitType')?.value;
    const targetUnitTypes = ['vacation_villa', 'chalets'];
    return targetUnitTypes.includes(unitType);
  }

  getSelectedAccessoriesText(): string {
    const input = this.currentInputs.find((i) => i.name === 'otherAccessories');
    if (!input) return '';
    return this.getSelectedText('otherAccessories', input.options);
  }

  isAccessorySelected(accessoryValue: string): boolean {
    return this.isMultiSelectOptionSelected('otherAccessories', accessoryValue);
  }

  toggleAccessory(accessoryValue: string): void {
    this.toggleMultiSelect('otherAccessories', accessoryValue);
  }

  getSelectedOtherExpensesText(): string {
    const input = this.currentInputs.find((i) => i.name === 'otherExpenses');
    if (!input) return '';
    return this.getSelectedText('otherExpenses', input.options);
  }

  isOtherExpenseSelected(expenseValue: string): boolean {
    return this.isMultiSelectOptionSelected('otherExpenses', expenseValue);
  }

  toggleOtherExpense(expenseValue: string): void {
    this.toggleMultiSelect('otherExpenses', expenseValue);
  }

  onAllAccessoriesChange(event: any): void {
    const input = this.currentInputs.find((i) => i.name === 'otherAccessories');
    if (!input) return;
    const isChecked = event.target.checked;
    const updatedValues = isChecked ? input.options.map((opt: any) => opt.value) : [];
    this.stepForms[this.currentStep - 1].patchValue({ otherAccessories: updatedValues });
    this.cdr.markForCheck();
  }

    getSelectedText(fieldName: string, options: any[]): string {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const selectedOptions = options.filter((opt) => currentValues.includes(opt.value));
    if (selectedOptions.length === 0) return '';
    if (selectedOptions.length === 1) return selectedOptions[0].key;
    return `${selectedOptions.length} items selected`;
  }

  isMultiSelectOptionSelected(fieldName: string, value: string): boolean {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    return currentValues.includes(value);
  }

  toggleMultiSelect(fieldName: string, value: string): void {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    let updatedValues;

    if (currentValues.includes(value)) {
      updatedValues = currentValues.filter((item: string) => item !== value);
    } else {
      updatedValues = [...currentValues, value];
    }

    this.getCurrentForm().patchValue({ [fieldName]: updatedValues });
    this.cdr.markForCheck();
  }

  // Enhanced multiselect methods
  getSelectedCount(fieldName: string): number {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    return currentValues.length;
  }

  getSearchQuery(fieldName: string): string {
    return this.searchQueries[fieldName] || '';
  }

  updateSearchQuery(fieldName: string, event: any): void {
    this.searchQueries[fieldName] = event.target.value;
    this.cdr.markForCheck();
  }

  clearSearch(fieldName: string): void {
    this.searchQueries[fieldName] = '';
    this.cdr.markForCheck();
  }

  getFilteredOptions(fieldName: string, options: any[]): any[] {
    const searchQuery = this.getSearchQuery(fieldName).toLowerCase();
    if (!searchQuery) {
      return options;
    }
    return options.filter(option =>
      option.key.toLowerCase().includes(searchQuery)
    );
  }

  areAllOptionsSelected(fieldName: string): boolean {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));
    return visibleOptions.length > 0 && visibleOptions.every(option => currentValues.includes(option.value));
  }

  areSomeOptionsSelected(fieldName: string): boolean {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));
    return visibleOptions.some(option => currentValues.includes(option.value));
  }

  toggleSelectAll(fieldName: string): void {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));

    if (this.areAllOptionsSelected(fieldName)) {
      // Deselect all visible options
      const updatedValues = currentValues.filter((value: string) =>
        !visibleOptions.some(option => option.value === value)
      );
      this.getCurrentForm().patchValue({ [fieldName]: updatedValues });
    } else {
      // Select all visible options
      const newValues = visibleOptions.map(option => option.value);
      const updatedValues = [...new Set([...currentValues, ...newValues])];
      this.getCurrentForm().patchValue({ [fieldName]: updatedValues });
    }
    this.cdr.markForCheck();
  }

  clearAllSelections(fieldName: string): void {
    this.getCurrentForm().patchValue({ [fieldName]: [] });
    this.cdr.markForCheck();
  }

  getSelectedOptions(fieldName: string, options: any[]): any[] {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    return options.filter(option => currentValues.includes(option.value));
  }

  private getOptionsForField(fieldName: string): any[] {
    const input = this.currentInputs.find(inp => inp.name === fieldName);
    return input?.options || [];
  }

  getText(options: { key: string; value: string }[], value: string): string {
    const item = options.find((item) => item.value === value);
    return item ? item.key : '';
  }

  select(form: FormGroup, field: string, value: string): void {
    form.patchValue({ [field]: value });
    form.get(field)?.markAsTouched();
    form.get(field)?.updateValueAndValidity();
    this.cdr.markForCheck();

    // Special handling for specialization scope changes
    if (field === 'specializationScope') {
      // Reset type and unitType when scope changes since filtered options may change
      form.patchValue({ type: '', unitType: '' });
      form.get('type')?.markAsUntouched();
      form.get('unitType')?.markAsUntouched();
      // Refresh the step inputs to update the unit type options
      this.loadStepInputs();
    } else if (field === 'type') {
      // Reset unitType when type changes
      form.patchValue({ unitType: '' });
      form.get('unitType')?.markAsUntouched();
    } else if (field === 'deliveryStatus') {
      // Trigger change detection for delivery date visibility
      this.cdr.detectChanges();
    } else if (field === 'floor') {
      // Trigger change detection for floor number visibility
      this.cdr.detectChanges();
    }
  }

  onSelectChange(fieldName: string, value: number, name: string): void {
    if (fieldName === 'cityId') {
      this.selectCity(value, name);
    } else if (fieldName === 'areaId') {
      this.selectArea(value, name);
    } else if (fieldName === 'subAreaId') {
      this.selectSubArea(value, name);
    }
  }

  fileValidator(options: { allowedTypes: string[]; maxSize: number }): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const file = control.value;
      if (!file) return null;

      if (file.size > options.maxSize) {
        return { maxSize: true };
      }

      if (!options.allowedTypes.includes(file.type)) {
        return { invalidType: true };
      }

      return null;
    };
  }

  fileArrayValidator(options: { allowedTypes: string[], maxSize: number }): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const files = control.value;

      if (!Array.isArray(files)) {
        return { notArray: true };
      }

      for (const file of files) {
        if (file.size > options.maxSize) {
          return { maxSize: true };
        }
        if (!options.allowedTypes.includes(file.type)) {
          return { invalidType: true };
        }
      }

      return null;
    };
  }

  isClient() {
    return this.userRole == 'client';
  }

  isMonthly() {
    const type = this.stepForms[2].get('rentDuration')?.value;
    if(type == 'monthly')
      {return true;}
    else return false;
  }

  shouldShowLocationSuggestions(): boolean {
    if (!this.isClient()) {
      return false;
    }


    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;

    if(type == 'sell' || type == 'rent_out'){
      return false;
    }
    // Show for inside compound scenarios
    if (this.getInsideCompoundPrivilege()) {
      return true;
    }

    // Show for purchase outside compound scenarios
    if (scope === 'purchase_sell_outside_compound' && type === 'purchasing') {
      return true;
    }

    // Show for rent-in outside compound scenarios (but not rent-out)
    if (scope === 'rentals_outside_compound' && type === 'rent_in') {
      return true;
    }

    return false;
  }

  getInsideCompoundPrivilege(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const targetScopes = [
      'primary_inside_compound',
      'resale_inside_compound',
      'rentals_inside_compound',
    ];
    return targetScopes.includes(scope);
  }

  getIDeliveryDatePrivilege(): boolean {
    const scope = this.stepForms[2].get('deliveryStatus')?.value;
    return scope == 'under_construction';
  }

  getIFloorNumberPrivilege(): boolean {
    const scope = this.stepForms[2].get('floor')?.value;
    return scope == 'repeated';
  }

   getIRequiredInsuranceValuePrivilege(): boolean {
    const scope = this.stepForms[4].get('rentRecurrence')?.value;
    return scope == 'fixed_amount';
  }

  getOutsideCompoundPrivilege(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const targetScopes = [
      'purchase_sell_outside_compound',
      'rentals_outside_compound',
    ];
    return targetScopes.includes(scope);
  }

  selectCity(cityId: number, cityName: string): void {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.getCurrentForm().patchValue({ cityId, areaId: '', subAreaId: '' });
    this.areasSubject.next([]);
    this.subAreasSubject.next([]);
    this.selectedAreaId = null;
    this.selectedAreaName = '';
    this.selectedSubAreaName = '';

    this.propertyService
      .getAreas(cityId)
      .pipe(
        map((response: any) => {
          const areas = response.data || response;
          return areas.map((area: any) => ({
            key: area.name_en,
            value: area.id,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching areas:', error);
          Swal.fire('Error', 'Failed to fetch areas.', 'error');
          return of([]);
        })
      )
      .subscribe((areas) => {
        console.log('Fetched areas:', areas);
        this.areasSubject.next(areas);
        this.cdr.markForCheck();
      });
  }

  selectArea(areaId: number, areaName: string): void {
    this.selectedAreaId = areaId;
    this.selectedAreaName = areaName;
    this.getCurrentForm().patchValue({ areaId, subAreaId: '' });
    this.subAreasSubject.next([]);
    this.selectedSubAreaName = '';

    this.propertyService
      .getSubAreas(areaId)
      .pipe(
        map((response: any) => {
          const subAreas = response.data || response;
          return subAreas.map((subArea: any) => ({
            key: subArea.name_en,
            value: subArea.id,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching sub-areas:', error);
          Swal.fire('Error', 'Failed to fetch sub-areas.', 'error');
          return of([]);
        })
      )
      .subscribe((subAreas) => {
        this.subAreasSubject.next(subAreas);
        this.cdr.markForCheck();
      });
  }

  selectSubArea(subAreaId: number, subAreaName: string): void {
    this.selectedSubAreaName = subAreaName;
    this.getCurrentForm().patchValue({ subAreaId });
    this.cdr.markForCheck();
  }

  getConfigKey(): string {
    const step1Values = this.stepForms[0].value;
    return `${step1Values.specializationScope}_${step1Values.type}_${step1Values.unitType}`;
  }

  getFilteredTypeOptions(): OptionItem[] {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    if (scope?.includes('rentals')) {
      return this.typeOptions.filter((t) => ['rent_out', 'rent_in'].includes(t.value));
    } else if (scope?.includes('purchase_sell') || scope?.includes('primary') || scope?.includes('resale')) {
      return this.typeOptions.filter((t) => ['purchasing', 'sell'].includes(t.value));
    }
    return this.typeOptions;
  }

    getFilteredUnitTypeOptions(): OptionItem[] {
    const scope = this.stepForms[0].get('specializationScope')?.value;

    // If no scope is selected, return empty array
    if (!scope) {
      return [];
    }

    // Inside compound unit types (primary and resale)
    if (scope === 'primary_inside_compound' || scope === 'resale_inside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'studios',
          'penthouses',
          // 'villas',
          'i_villa',
          'twin_houses',
          'town_houses',
          'standalone_villas',
          'administrative_units',
          // 'commercial_units',
          'medical_clinics',
          // 'commercial_stores',
          'pharmacies',
          'commercial_administrative_buildings',
          'shops',
          'vacation_villa',
          'chalets'
        ].includes(unitType.value)
      );
    }

    //  if (selectedScope === 'purchase_sell_outside_compound' ||
    //     // selectedScope === 'purchase_sell_inside_compound' ||
    //     selectedScope === 'primary_inside_compound' ||
    //     selectedScope === 'resale_inside_compound') {
    //    return this.Type.filter(type =>
    //     type.value === 'sell' || type.value === 'purchasing'
    // Outside compound unit types (purchase-sell)
    if (scope === 'purchase_sell_outside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'penthouses',
          'studios',
          'basements',
          'roofs',
          'administrative_units',
          'medical_clinics',
          'pharmacies',
          'commercial_stores',
          'standalone_villas',
          // 'factory_lands',
          // 'warehouses',
          'residential_buildings',
          'residential_lands',
          'commercial_administrative_buildings',
          'vacation_villa',
          'chalets'
        ].includes(unitType.value)
      );
    }

    // Rental unit types (both inside and outside compound)
    if (scope === 'rentals_inside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'studios',
          'penthouses',
          'villas',
          'town_houses',
          'twin_houses',
          // 'basements',
          // 'roofs',
          // 'residential_buildings',
          'standalone_villas',
          'administrative_units',
          'commercial_stores',
          'medical_clinics',
          'pharmacies',
          'commercial_administrative_buildings',
          'vacation_villa',
          'chalets',
          'hotels'
        ].includes(unitType.value)
      );
    }

    if (scope === 'rentals_outside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'studios',
          'penthouses',
          // 'villas',
          // 'town_houses',
          // 'twin_houses',
          'basements',
          'roofs',
          // 'residential_buildings',
          'standalone_villas',
          'administrative_units',
          'commercial_stores',
          'medical_clinics',
          'pharmacies',
          'factory_lands',
          'warehouses',
          'commercial_administrative_buildings',
          'vacation_villa',
          'chalets',
          'hotels'
        ].includes(unitType.value)
      );
    }

    // For other scopes, return all unit types
    return this.unitTypeOptions;
  }

  getCurrentForm(): FormGroup {
    return this.stepForms[this.currentStep - 1];
  }

  nextStep(): void {
    if (this.getCurrentForm().valid && this.currentStep < this.totalSteps) {
      this.saveCurrentStepData();
      this.currentStep++;

      // Skip step 4 if it should be skipped
      if (this.currentStep === 4 && this.shouldSkipStep4()) {
        this.currentStep++;
      }

      this.loadStepInputs();
    }
  }

  prevStep(): void {
    if (this.currentStep > 1) {
      this.saveCurrentStepData();
      this.currentStep--;

      // Skip step 4 if it should be skipped (when going backwards)
      if (this.currentStep === 4 && this.shouldSkipStep4()) {
        this.currentStep--;
      }

      this.loadStepInputs();
    }
  }

  // Save current step data to preserve it
  private saveCurrentStepData(): void {
    const currentForm = this.getCurrentForm();
    if (currentForm && currentForm.value) {
      console.log(`Saving data for step ${this.currentStep}:`, currentForm.value);
      console.log(`Form controls for step ${this.currentStep}:`, Object.keys(currentForm.controls));

      // Ensure all form values are properly saved
      const formValue = currentForm.value;
      Object.keys(formValue).forEach(key => {
        if (formValue[key] !== null && formValue[key] !== undefined && formValue[key] !== '') {
          console.log(`Preserved field ${key}:`, formValue[key]);
        }
      });
    }
  }


  // Check if current step has inputs
  hasStepInputs(): boolean {
    return this.currentInputs && this.currentInputs.length > 0;
  }

  // Check if input is required
  isInputRequired(input: any): boolean {
    if(input == null){ return false;}
    return input.validators && input.validators.some((validator: any) =>
      validator === Validators.required ||
      (validator && validator.toString && validator.toString().includes('required'))
    );
  }

  // Check if step 4 should be skipped
  shouldSkipStep4(): boolean {
    if (!this.stepForms[0]) {
      return false; // Don't skip if form doesn't exist yet
    }
    const type = this.stepForms[0].get('type')?.value;
    return type === 'purchasing' || type === 'rent_in';
  }

  // Get step 4 info message
  getStep4InfoMessage(): string {
    const configKey = this.getConfigKey();
    if (configKey.includes('rent_in')) {
      return 'No image uploads are required for rent-in requests.';
    }
    return 'No image uploads are available for this property type.';
  }

  trackByInputName(index: number, input: any): string {
    return input.name;
  }

  // Method to determine if input should be shown
  shouldShowInput(input: any): boolean {
    const formValue = this.getCurrentForm().value;

    // Special handling for deliveryDate
    if (input.name === 'deliveryDate') {
      return formValue?.deliveryStatus === 'under_construction';
    }

    // Special handling for floorNumber
    if (input.name === 'floorNumber') {
      return formValue?.floor === 'repeated';
    }

    // Special handling for floorNumber
    if (input.name === 'requiredInsuranceValue') {
      return formValue?.requiredInsurance === 'fixed_amount';
    }

    // For all other inputs, use their visibility function
    return input.visibility();
  }

  navigateToErrorStep(stepNumber: number): void {
    // If trying to navigate to step 4 and it should be skipped, go to step 5 instead
    if (stepNumber === 4 && this.shouldSkipStep4()) {
      this.currentStep = 5;
    } else {
      this.currentStep = stepNumber;
    }
    this.loadStepInputs();
    this.cdr.markForCheck();
  }

  clearValidationErrors(): void {
    this.validationErrors = [];
    this.showErrorList = false;
    this.cdr.markForCheck();
  }

  /**
   * Get total count of all validation errors across all steps
   */
  getTotalErrorCount(): number {
    return this.validationErrors.reduce((total, stepError) => {
      return total + stepError.errors.length;
    }, 0);
  }

  /**
   * Navigate to the first step that has validation errors
   */
  navigateToFirstErrorStep(): void {
    if (this.validationErrors.length > 0) {
      const firstErrorStep = Math.min(...this.validationErrors.map(error => error.step));
      this.navigateToErrorStep(firstErrorStep);
    }
  }

  isCurrentFormValid(): boolean {
    return this.getCurrentForm().valid;
  }

  // Debug method to show current form errors
  getCurrentFormErrors(): string {
    const form = this.getCurrentForm();
    const errors: string[] = [];

    Object.keys(form.controls).forEach(key => {
      const control = form.get(key);
      if (control && control.invalid) {
        const controlErrors = control.errors;
        if (controlErrors) {
          Object.keys(controlErrors).forEach(errorKey => {
            errors.push(`${key}: ${errorKey}`);
          });
        }
      }
    });

    return errors.length > 0 ? errors.join(', ') : 'No errors';
  }

  getFileCount(fieldName: string): number {
    const files = this.getCurrentForm().get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  onFileChange(event: any, fieldName: string): void {
    if (event.target.files?.length) {
      this.stepForms[this.currentStep - 1].patchValue({
        [fieldName]: Array.from(event.target.files)
      });
      this.cdr.markForCheck();
    }
  }

  submitForm(): void {
    if (this.stepForms.every((form) => form.valid)) {
      this.isSubmitting = true;
      const formData = new FormData();
      formData.append('userId', this.userId.toString());

      // Add step 1 values
      const step1Values = this.stepForms[0].value;
      Object.keys(step1Values).forEach((key) => {
        formData.append(key, step1Values[key] || '');
      });

      // Add location data from step 2
      const step2Values = this.stepForms[1].value;
      if (step2Values.cityId) {
        formData.append('locations[0][city]', step2Values.cityId.toString());
      }
      if (step2Values.areaId) {
        formData.append('locations[0][areas][0][id]', step2Values.areaId.toString());
      }
      if (step2Values.subAreaId) {
        formData.append('locations[0][areas][0][subAreas][0]', step2Values.subAreaId.toString());
      }

      // Add other form values
      this.stepForms.slice(1).forEach((form) => {
        Object.keys(form.value).forEach((key) => {
          if (['cityId', 'areaId', 'subAreaId'].includes(key)) {
            return;
          }
          const value = form.value[key];
          if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
              value.forEach((item: any, i: number) =>
                formData.append(`attributes[${key}][${i}]`, item)
              );
            } else if (typeof value === 'boolean') {
              formData.append(`attributes[${key}]`, value ? '1' : '0');
            } else if (value !== '') {
              formData.append(`attributes[${key}]`, value.toString());
            }
          }
        });
      });

      // Handle file uploads
      const fileFields = ['mainImage', 'galleryImages', 'video', 'unitInMasterPlanImage'];
      fileFields.forEach((field) => {
        const files = this.stepForms[3]?.get(field)?.value;
        if (files && Array.isArray(files) && files.length > 0) {
          const isMultiple = ['galleryImages', 'video'].includes(field);

          if (isMultiple) {
            files.forEach((file: File, index: number) => {
              if (file instanceof File) {
                formData.append(`attributes[${field}][${index}]`, file);
              }
            });
          } else {
            if (files[0] instanceof File) {
              formData.append(`attributes[${field}]`, files[0]);
            }
          }
        }
      });

      this.createRequestService.createRequest(formData).subscribe({
        next: (res) => {
          this.isSubmitting = false;
          Swal.fire('Success', 'Request created successfully!', 'success').then(() => {
            this.router.navigate([`/requests/assign-to`, res.data?.id || res.id]);
          });
        },
        error: (err) => {
          this.isSubmitting = false;
          this.handleSubmissionError(err);
        },
      });
    }
  }

  handleSubmissionError(error: any): void {
    this.validationErrors = [];
    if (error?.error?.errors) {
      Object.keys(error.error.errors).forEach((field) => {
        const cleanField = field.replace(/^attributes\./g, '').replace(/\[\d+\].*/g, '');
        const step = this.currentInputs.find((input) => input.name === cleanField)?.step || 1;
        let stepGroup = this.validationErrors.find((v) => v.step === step);
        if (!stepGroup) {
          stepGroup = {
            step,
            stepName: this.stepNames[step] || `Step ${step}`,
            errors: []
          };
          this.validationErrors.push(stepGroup);
        }
        stepGroup.errors.push({
          field: cleanField,
          messages: Array.isArray(error.error.errors[field])
            ? error.error.errors[field]
            : [error.error.errors[field]],
        });
      });
      this.showErrorList = true;
      this.cdr.markForCheck();
    } else {
      Swal.fire('Error', error?.error?.message || 'An unknown error occurred.', 'error');
    }
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'STEP': 'الخطوة',
        'OF': 'من'
      },
      'en': {
        'STEP': 'Step',
        'OF': 'of'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  // Get translated option text
  getTranslatedOptionText(options: any[], value: string): string {
    const option = options.find(opt => opt.value === value);
    if (!option) return '';

    // Check if it's a translatable option with translationKey
    if (option.translationKey) {
      // Try different translation paths
      const translations = [
        `STEPPER_MODAL.SPECIALIZATION_SCOPE_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.TYPE_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.UNIT_TYPE_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.FLOOR_TYPES_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.BUILDING_LICENSE_TYPES_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.UNIT_LAYOUT_STATUS_TYPES_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.PAYMENT_METHOD_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.FURNISHING_STATUS_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.DELIVERY_STATUS_TYPES_OPTIONS.${option.translationKey}`,
        `STEPPER_MODAL.FINISHING_STATUS_TYPES_OPTIONS.${option.translationKey}`
      ];

      for (const translationPath of translations) {
        const translated = this.translate.instant(translationPath);
        if (translated !== translationPath) {
          return translated;
        }
      }
    }

    return option.key;
  }

  // Get translated step name
  getTranslatedStepName(stepNumber: number): string {
    const stepKeys = {
      1: 'REQUEST_SETTINGS',
      2: 'LOCATION_INFORMATION',
      3: 'UNIT_INFORMATION',
      4: 'PROJECT_DOCUMENTS',
      5: 'FINANCIAL_INFORMATION'
    };

    const key = stepKeys[stepNumber as keyof typeof stepKeys];
    return key ? this.translate.instant(`STEPPER_MODAL.STEPS.${key}`) : `Step ${stepNumber}`;
  }

  // Get translated label
  getTranslatedLabel(key: string): string {
    return this.translate.instant(`STEPPER_MODAL.LABELS.${key}`);
  }

  // Get translated placeholder
  getTranslatedPlaceholder(key: string): string {
    return this.translate.instant(`STEPPER_MODAL.PLACEHOLDERS.${key}`);
  }

  // Get translated validation message
  getTranslatedValidation(key: string): string {
    return this.translate.instant(`STEPPER_MODAL.VALIDATION.${key}`);
  }

  // Get unit type translation from PROPERTY_TYPES
  getUnitTypeTranslation(value: string): string {
    const currentLang = this.translationService.getCurrentLanguage();

    // Check if translation exists in PROPERTY_TYPES
    if (PROPERTY_TYPES[value]) {
      return PROPERTY_TYPES[value][currentLang as 'en' | 'ar'] || PROPERTY_TYPES[value]['en'];
    }

    // Fallback to finding in unitTypeOptions
    const option = this.unitTypeOptions.find(opt => opt.value === value);
    return option ? option.key : value;
  }

  // Get translated input label
  getTranslatedInputLabel(originalLabel: string, inputName: string): string {
    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar') {
      // Arabic translations for common input labels
      const arabicLabels: { [key: string]: string } = {
        // Common field names
        'Area': 'المساحة',
        'Price': 'السعر',
        'Rooms': 'الغرف',
        'Bathrooms': 'الحمامات',
        'Floor': 'الدور',
        'Floor Number': 'رقم الدور',
        'Building Age': 'عمر المبنى',
        'Delivery Date': 'تاريخ التسليم',
        'Down Payment': 'المقدم',
        'Monthly Installment': 'القسط الشهري',
        'Installment Period': 'فترة التقسيط',
        'Maintenance': 'الصيانة',
        'Notes': 'ملاحظات',
        'Description': 'الوصف',
        'Features': 'المميزات',
        'Amenities': 'الخدمات',
        'Parking': 'موقف السيارات',
        'Garden': 'حديقة',
        'Pool': 'حمام سباحة',
        'Gym': 'صالة رياضية',
        'Security': 'الأمن',
        'Elevator': 'مصعد',
        'Balcony': 'شرفة',
        'Terrace': 'تراس',
        'Storage': 'مخزن',
        'Maid Room': 'غرفة خادمة',
        'Driver Room': 'غرفة سائق',
        'Laundry Room': 'غرفة غسيل',
        'Study Room': 'غرفة مكتب',
        'Guest Room': 'غرفة ضيوف',
        'Master Bedroom': 'غرفة نوم رئيسية',
        'Living Room': 'غرفة معيشة',
        'Dining Room': 'غرفة طعام',
        'Kitchen': 'مطبخ',
        'Reception': 'استقبال',
        'Hall': 'صالة',
        'Entrance': 'مدخل',
        'Finishing': 'التشطيب',
        'Finishing Type': 'نوع التشطيب',
        'Payment Method': 'طريقة الدفع',
        'Furnishing': 'الفرش',
        'Furnishing Status': 'حالة الفرش',
        'View': 'الإطلالة',
        'Orientation': 'الاتجاه',
        'Location Suggestions': 'اقتراحات الموقع',
        'Budget Suggestions': 'اقتراحات الميزانية',
        'Rent Price Suggestions': 'اقتراحات سعر الإيجار'
      };

      // Check if we have a direct translation
      if (arabicLabels[originalLabel]) {
        return arabicLabels[originalLabel];
      }

      // Check by input name for specific cases
      const nameBasedLabels: { [key: string]: string } = {
        'area': 'المساحة',
        'price': 'السعر',
        'rooms': 'الغرف',
        'bathrooms': 'الحمامات',
        'floor': 'الدور',
        'floorNumber': 'رقم الدور',
        'buildingAge': 'عمر المبنى',
        'deliveryDate': 'تاريخ التسليم',
        'downPayment': 'المقدم',
        'monthlyInstallment': 'القسط الشهري',
        'installmentPeriod': 'فترة التقسيط',
        'maintenance': 'الصيانة',
        'notes': 'ملاحظات',
        'description': 'الوصف',
        'locationSuggestions': 'اقتراحات الموقع',
        'budgetSuggestions': 'اقتراحات الميزانية',
        'rentPriceSuggestions': 'اقتراحات سعر الإيجار'
      };

      if (nameBasedLabels[inputName]) {
        return nameBasedLabels[inputName];
      }
    }

    return originalLabel;
  }

  // Get translated dropdown value (for selected option display)
  getTranslatedDropdownValue(options: any[], value: string, inputName: string): string {
    if (!value || !options) return '';

    const option = options.find(opt => opt.value === value);
    if (!option) return '';

    return this.getTranslatedDropdownOption(option, inputName);
  }

  // Get translated dropdown option
  getTranslatedDropdownOption(option: any, inputName: string): string {
    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar') {
      // Arabic translations for common dropdown options
      const arabicOptions: { [key: string]: string } = {
        // Floor types
        'ground': 'الدور الأرضي',
        'last_floor': 'الدور الأخير',
        'repeated': 'متكرر',
        'all_the_above_are_suitable': 'جميع ما سبق مناسب',

        // Building license types
        'permit_available': 'تصريح متاح',
        'no_permit': 'لا يوجد تصريح',

        // Unit layout status
        'partial_roof': 'سقف جزئي',
        'full_roof': 'سقف كامل',
        'open_space': 'مساحة مفتوحة',
        'single_apartment': 'شقة واحدة',
        'two_apartments': 'شقتان',
        'all_acceptable': 'جميع المقبول',

        // Payment methods
        'cash': 'نقدي',
        'installment': 'تقسيط',
        'all_of_the_above_are_suitable': 'جميع ما سبق مناسب',

        // Furnishing status
        'unfurnished': 'غير مفروش',
        'furnished_with_air_conditioners': 'مفروش مع مكيفات',
        'furnished_without_air_conditioners': 'مفروش بدون مكيفات',
        'all_of_the_above': 'جميع ما سبق',

        // Delivery status
        'immediate_delivery': 'تسليم فوري',
        'under_construction': 'تحت الإنشاء',

        // Finishing status
        'on_brick': 'على الطوب',
        'semi_finished': 'نصف تشطيب',
        'company_finished': 'تشطيب شركة',
        'full_finished': 'تشطيب كامل',
        'super_lux': 'سوبر لوكس',
        'ultra_super_lux': 'ألترا سوبر لوكس',

        // Common yes/no options
        'yes': 'نعم',
        'no': 'لا',
        'available': 'متاح',
        'not_available': 'غير متاح',
        'required': 'مطلوب',
        'optional': 'اختياري',

        // Views
        'sea_view': 'إطلالة بحرية',
        'garden_view': 'إطلالة حديقة',
        'street_view': 'إطلالة شارع',
        'pool_view': 'إطلالة حمام سباحة',
        'city_view': 'إطلالة مدينة',

        // Orientations
        'north': 'شمال',
        'south': 'جنوب',
        'east': 'شرق',
        'west': 'غرب',
        'north_east': 'شمال شرق',
        'north_west': 'شمال غرب',
        'south_east': 'جنوب شرق',
        'south_west': 'جنوب غرب'
      };

      // Check if we have a direct translation for the option value
      if (arabicOptions[option.value]) {
        return arabicOptions[option.value];
      }

      // Check if we have a direct translation for the option key
      if (arabicOptions[option.key?.toLowerCase()]) {
        return arabicOptions[option.key.toLowerCase()];
      }
    }

    return option.key || option.value;
  }

  // Get translated min value error message
  getTranslatedMinValueError(minValue: number): string {
    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar') {
      return `يجب أن يكون أكبر من أو يساوي ${minValue}`;
    }

    return `must be greater than or equal to ${minValue}`;
  }

  // Get translated city name
  getTranslatedCityName(cityName: string): string {
    if (!cityName) return '';

    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar') {
      // Map of city names to translation keys
      const cityTranslationMap: { [key: string]: string } = {
        'Cairo': 'CAIRO',
        'Giza': 'GIZA',
        'Alexandria': 'ALEXANDRIA',
        'Luxor': 'LUXOR',
        'Aswan': 'ASWAN',
        'Sharm El Sheikh': 'SHARM_EL_SHEIKH',
        'Hurghada': 'HURGHADA',
        'Mansoura': 'MANSOURA',
        'Tanta': 'TANTA',
        'Ismailia': 'ISMAILIA',
        'Suez': 'SUEZ',
        'Port Said': 'PORT_SAID',
        'Damietta': 'DAMIETTA',
        'Kafr El Sheikh': 'KAFR_EL_SHEIKH',
        'Beni Suef': 'BENI_SUEF',
        'Minya': 'MINYA',
        'Asyut': 'ASYUT',
        'Sohag': 'SOHAG',
        'Qena': 'QENA',
        'Red Sea': 'RED_SEA',
        'New Valley': 'NEW_VALLEY',
        'Matrouh': 'MATROUH',
        'North Sinai': 'NORTH_SINAI',
        'South Sinai': 'SOUTH_SINAI',
        'Fayyum': 'FAYYUM',
        'Beheira': 'BEHEIRA',
        'Dakahlia': 'DAKAHLIA',
        'Sharqia': 'SHARQIA',
        'Monufia': 'MONUFIA',
        'Gharbia': 'GHARBIA',
        'Qalyubia': 'QALYUBIA',
        '10th of Ramadan': '10TH_OF_RAMADAN',
        'Badr': 'BADR',
        'New Cairo': 'NEW_CAIRO',
        'Sheikh Zayed': 'SHEIKH_ZAYED',
        '6th of October': '6TH_OF_OCTOBER',
        'Obour': 'OBOUR',
        'Shorouk': 'SHOROUK',
        'New Administrative Capital': 'NEW_ADMINISTRATIVE_CAPITAL'
      };

      const translationKey = cityTranslationMap[cityName];
      if (translationKey) {
        const translated = this.translate.instant(`CITIES.${translationKey}`);
        if (translated !== `CITIES.${translationKey}`) {
          return translated;
        }
      }
    }

    return cityName;
  }

  // Get translated area name
  getTranslatedAreaName(areaName: string): string {
    if (!areaName) return '';

    const currentLang = this.translationService.getCurrentLanguage();

    if (currentLang === 'ar') {
      // Common area translations - you can expand this based on your data
      const areaTranslations: { [key: string]: string } = {
        'Downtown': 'وسط البلد',
        'Maadi': 'المعادي',
        'Zamalek': 'الزمالك',
        'Heliopolis': 'مصر الجديدة',
        'Nasr City': 'مدينة نصر',
        'New Cairo': 'القاهرة الجديدة',
        'Sheikh Zayed': 'الشيخ زايد',
        '6th of October': 'السادس من أكتوبر',
        'Dokki': 'الدقي',
        'Mohandessin': 'المهندسين',
        'Agouza': 'العجوزة',
        'Garden City': 'جاردن سيتي',
        'Abbasia': 'العباسية',
        'Shubra': 'شبرا',
        'Ain Shams': 'عين شمس',
        'Mataria': 'المطرية',
        'Zeitoun': 'الزيتون',
        'Manial': 'المنيل',
        'Old Cairo': 'مصر القديمة',
        'Sayeda Zeinab': 'السيدة زينب',
        'Bab El Louk': 'باب اللوق',
        'Tahrir': 'التحرير',
        'Opera': 'الأوبرا',
        'Kasr El Nil': 'قصر النيل',
        'Rod El Farag': 'روض الفرج',
        'Shoubra El Kheima': 'شبرا الخيمة',
        'Qalyub': 'قليوب',
        'Khanka': 'الخانكة',
        'Obour': 'العبور',
        'Shorouk': 'الشروق',
        'Badr': 'بدر',
        '10th of Ramadan': 'العاشر من رمضان'
      };

      if (areaTranslations[areaName]) {
        return areaTranslations[areaName];
      }
    }

    return areaName;
  }
}
